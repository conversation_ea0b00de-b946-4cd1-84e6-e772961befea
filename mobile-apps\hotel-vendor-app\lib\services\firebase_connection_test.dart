import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirebaseConnectionTest {
  static final FirebaseConnectionTest _instance = FirebaseConnectionTest._internal();
  factory FirebaseConnectionTest() => _instance;
  FirebaseConnectionTest._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Test Firebase Core initialization
  Future<bool> testFirebaseCore() async {
    try {
      final app = Firebase.app();
      print('✅ Firebase Core: Connected to project ${app.options.projectId}');
      return true;
    } catch (e) {
      print('❌ Firebase Core: Connection failed - $e');
      return false;
    }
  }

  /// Test Firestore connection
  Future<bool> testFirestore() async {
    try {
      // Try to write a test document
      final testDoc = _firestore.collection('connection_test').doc('test');
      await testDoc.set({
        'timestamp': FieldValue.serverTimestamp(),
        'message': 'Firebase connection test successful',
        'app': 'hotel_vendor_app',
      });

      // Try to read the document back
      final docSnapshot = await testDoc.get();
      if (docSnapshot.exists) {
        print('✅ Firestore: Read/Write operations successful');
        
        // Clean up test document
        await testDoc.delete();
        print('✅ Firestore: Test document cleaned up');
        return true;
      } else {
        print('❌ Firestore: Document write failed');
        return false;
      }
    } catch (e) {
      print('❌ Firestore: Connection failed - $e');
      return false;
    }
  }

  /// Test Firebase Auth connection
  Future<bool> testFirebaseAuth() async {
    try {
      // Check if auth is available
      final currentUser = _auth.currentUser;
      print('✅ Firebase Auth: Service available (Current user: ${currentUser?.email ?? 'None'})');
      return true;
    } catch (e) {
      print('❌ Firebase Auth: Connection failed - $e');
      return false;
    }
  }

  /// Run all Firebase connection tests
  Future<Map<String, bool>> runAllTests() async {
    print('🔥 Starting Firebase Connection Tests...\n');
    
    final results = <String, bool>{};
    
    // Test Firebase Core
    results['firebase_core'] = await testFirebaseCore();
    
    // Test Firestore
    results['firestore'] = await testFirestore();
    
    // Test Firebase Auth
    results['firebase_auth'] = await testFirebaseAuth();
    
    // Summary
    final allPassed = results.values.every((test) => test);
    print('\n📊 Firebase Connection Test Results:');
    results.forEach((service, passed) {
      print('   ${passed ? '✅' : '❌'} $service: ${passed ? 'PASSED' : 'FAILED'}');
    });
    
    print('\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}');
    
    return results;
  }

  /// Test specific collections that the app uses
  Future<bool> testAppCollections() async {
    try {
      print('\n🏨 Testing Hotel Vendor App Collections...');
      
      // Test vendors collection
      final vendorsRef = _firestore.collection('vendors');
      await vendorsRef.limit(1).get();
      print('✅ Vendors collection: Accessible');
      
      // Test hotels collection
      final hotelsRef = _firestore.collection('hotels');
      await hotelsRef.limit(1).get();
      print('✅ Hotels collection: Accessible');
      
      // Test bookings collection
      final bookingsRef = _firestore.collection('bookings');
      await bookingsRef.limit(1).get();
      print('✅ Bookings collection: Accessible');
      
      // Test guests collection
      final guestsRef = _firestore.collection('guests');
      await guestsRef.limit(1).get();
      print('✅ Guests collection: Accessible');
      
      // Test rooms collection
      final roomsRef = _firestore.collection('rooms');
      await roomsRef.limit(1).get();
      print('✅ Rooms collection: Accessible');
      
      print('✅ All app collections are accessible');
      return true;
    } catch (e) {
      print('❌ App collections test failed: $e');
      return false;
    }
  }
}
