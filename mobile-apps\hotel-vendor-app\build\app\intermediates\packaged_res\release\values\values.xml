<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="default_web_client_id" translatable="false">408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com</string>
    <string name="gcm_defaultSenderId" translatable="false">408299360705</string>
    <string name="google_api_key" translatable="false">AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg</string>
    <string name="google_app_id" translatable="false">1:408299360705:android:553b6964daacc3dfdd5f40</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg</string>
    <string name="google_storage_bucket" translatable="false">linkinblink-f544a.firebasestorage.app</string>
    <string name="project_id" translatable="false">linkinblink-f544a</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>