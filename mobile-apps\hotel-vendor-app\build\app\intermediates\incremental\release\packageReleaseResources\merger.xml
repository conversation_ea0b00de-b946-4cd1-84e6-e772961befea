<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res"><file name="launch_background" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="launch_background" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="launcher_icon" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-hdpi\launcher_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="launcher_icon" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-mdpi\launcher_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="launcher_icon" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-xhdpi\launcher_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-xxhdpi\launcher_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\mipmap-xxxhdpi\launcher_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\generated\res\resValues\release"/><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\generated\res\resValues\release"/><source path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\generated\res\processReleaseGoogleServices"><file path="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">408299360705</string><string name="google_api_key" translatable="false">AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg</string><string name="google_app_id" translatable="false">1:408299360705:android:553b6964daacc3dfdd5f40</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg</string><string name="google_storage_bucket" translatable="false">linkinblink-f544a.firebasestorage.app</string><string name="project_id" translatable="false">linkinblink-f544a</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>