import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import 'hotel_logo.dart';

class VendorAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottom;
  final bool showLogo;
  final double logoSize;

  const VendorAppBar({
    super.key,
    required this.title,
    this.actions,
    this.bottom,
    this.showLogo = true,
    this.logoSize = 40,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: showLogo
          ? Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Image.asset(
                    'assets/logos/link_in_blink_black.png',
                    width: logoSize,
                    height: logoSize,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return HotelLogoSmall(size: logoSize);
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            )
          : Text(title),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      actions: actions,
      bottom: bottom,
      elevation: 2,
    );
  }

  @override
  Size get preferredSize {
    final double height = kToolbarHeight + (bottom?.preferredSize.height ?? 0.0);
    return Size.fromHeight(height);
  }
}
