{"logs": [{"outputFile": "com.example.hotel_vendor_app-mergeReleaseResources-52:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b75b0bc0a35aca6c9e40ab5eafc895e3\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "431", "startColumns": "4", "startOffsets": "27541", "endColumns": "81", "endOffsets": "27618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb3da94f78dbd53819b98ecc64aa5b0b\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "232,233,234,242,243,244,319,3464", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13968,14027,14075,14742,14817,14893,19395,186223", "endLines": "232,233,234,242,243,244,319,3483", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14022,14070,14126,14812,14888,14960,19456,187013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\736f5b2928c9652cb9c17ae2bb206c07\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "316,317,322,329,330,349,350,351,352,353", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19274,19314,19531,19869,19924,20941,20995,21047,21096,21157", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19309,19356,19569,19919,19966,20990,21042,21091,21152,21202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f0075b67fb420a2a695ecf8b193e7e2\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "358", "startColumns": "4", "startOffsets": "21385", "endColumns": "53", "endOffsets": "21434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ffe8c5202c8aee5e22f6886f6465e53a\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,122,260,261,262,263,264,265,266,323,324,325,366,367,424,427,437,438,444,445,446,1519,1703,1706,1712,1718,1721,1727,1731,1734,1741,1747,1750,1756,1761,1766,1773,1775,1781,1787,1795,1800,1807,1812,1818,1822,1829,1833,1839,1845,1848,1852,1853,2778,2793,2932,2970,3112,3300,3318,3382,3392,3402,3409,3415,3519,3688,3705", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6377,15943,16007,16062,16130,16197,16262,16319,19574,19622,19670,21864,21927,27019,27269,28147,28191,28533,28672,28722,96928,110666,110771,111016,111354,111500,111840,112052,112215,112622,112960,113083,113422,113661,113918,114289,114349,114687,114973,115422,115714,116102,116407,116751,116996,117326,117533,117801,118074,118218,118419,118466,161461,161984,168770,170071,175013,180923,181551,183476,183758,184063,184325,184585,188101,194396,194926", "endLines": "63,122,260,261,262,263,264,265,266,323,324,325,366,367,424,427,437,440,444,445,446,1535,1705,1711,1717,1720,1726,1730,1733,1740,1746,1749,1755,1760,1765,1772,1774,1780,1786,1794,1799,1806,1811,1817,1821,1828,1832,1838,1844,1847,1851,1852,1853,2782,2803,2951,2973,3121,3307,3381,3391,3401,3408,3414,3457,3531,3704,3721", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6441,16002,16057,16125,16192,16257,16314,16371,19617,19665,19726,21922,21985,27052,27321,28186,28326,28667,28717,28765,98361,110766,111011,111349,111495,111835,112047,112210,112617,112955,113078,113417,113656,113913,114284,114344,114682,114968,115417,115709,116097,116402,116746,116991,117321,117528,117796,118069,118213,118414,118461,118517,161641,162380,169494,170215,175340,181166,183471,183753,184058,184320,184580,186003,188548,194921,195489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e15cab99671a1cbe244cc623f2c5226d\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2015", "startColumns": "4", "startOffsets": "131056", "endLines": "2022", "endColumns": "8", "endOffsets": "131461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6556e295967070d7cb434cb97ac51959\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2132,2842,2848", "startColumns": "4,4,4,4", "startOffsets": "164,140264,164043,164254", "endLines": "3,2134,2847,2931", "endColumns": "60,12,24,24", "endOffsets": "220,140404,164249,168765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b79d0049673370d40f2ac65d8e1e2574\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "21239", "endColumns": "42", "endOffsets": "21277"}}, {"source": "E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\build\\app\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,723", "endColumns": "143,81,103,108,119,108,77", "endOffsets": "194,276,380,489,609,718,796"}, "to": {"startLines": "426,432,433,434,435,436,441", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27125,27623,27705,27809,27918,28038,28331", "endColumns": "143,81,103,108,119,108,77", "endOffsets": "27264,27700,27804,27913,28033,28142,28404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\772e2867940e4356072472f7dc271176\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,235,236,237,238,239,240,241,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,361,362,363,364,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,442,447,448,449,450,451,452,460,461,465,469,473,478,484,491,495,499,504,508,512,516,520,524,528,534,538,544,548,554,558,563,567,570,574,580,584,590,594,600,603,607,611,615,619,623,624,625,626,629,632,635,638,642,643,644,645,646,649,651,653,655,660,661,665,671,675,676,678,689,690,694,700,704,705,706,710,737,741,742,746,774,944,970,1141,1167,1198,1206,1212,1226,1248,1253,1258,1268,1277,1286,1290,1297,1305,1312,1313,1322,1325,1328,1332,1336,1340,1343,1344,1349,1354,1364,1369,1376,1382,1383,1386,1390,1395,1397,1399,1402,1405,1407,1411,1414,1421,1424,1427,1431,1433,1437,1439,1441,1443,1447,1455,1463,1475,1481,1490,1493,1504,1507,1508,1513,1514,1543,1612,1682,1683,1693,1702,1854,1856,1860,1863,1866,1869,1872,1875,1878,1881,1885,1888,1891,1894,1898,1901,1905,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1931,1933,1934,1935,1936,1937,1938,1939,1940,1942,1943,1945,1946,1948,1950,1951,1953,1954,1955,1956,1957,1958,1960,1961,1962,1963,1964,1976,1978,1980,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1998,1999,2000,2001,2003,2007,2023,2024,2025,2026,2027,2028,2032,2033,2034,2035,2037,2039,2041,2043,2045,2046,2047,2048,2050,2052,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2068,2069,2070,2071,2073,2075,2076,2078,2079,2081,2083,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2098,2099,2100,2101,2103,2104,2105,2106,2107,2109,2111,2113,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2135,2210,2213,2216,2219,2233,2250,2292,2321,2348,2357,2419,2783,2814,2952,3076,3100,3106,3135,3156,3280,3308,3314,3458,3484,3551,3622,3722,3742,3797,3809,3835", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5573,5632,5691,5750,5809,5868,5922,5976,6029,6083,6137,6191,6446,6520,6599,6672,6746,6817,6889,6961,7034,7091,7149,7222,7296,7370,7445,7517,7590,7660,7731,7791,7852,7921,7990,8060,8134,8210,8274,8351,8427,8504,8569,8638,8715,8790,8859,8927,9004,9070,9131,9228,9293,9362,9461,9532,9591,9649,9706,9765,9829,9900,9972,10044,10116,10188,10255,10323,10391,10450,10513,10577,10667,10758,10818,10884,10951,11017,11087,11151,11204,11271,11332,11399,11512,11570,11633,11698,11763,11838,11911,11983,12032,12093,12154,12215,12277,12341,12405,12469,12534,12597,12657,12718,12784,12843,12903,12965,13036,13096,13795,13881,14131,14221,14308,14396,14478,14561,14651,16376,16428,16486,16531,16597,16661,16718,16775,18952,19009,19057,19106,19361,19731,19778,20036,21207,21553,21617,21679,21739,22060,22134,22204,22282,22336,22406,22491,22539,22585,22646,22709,22775,22839,22910,22973,23038,23102,23163,23224,23276,23349,23423,23492,23567,23641,23715,23856,28409,28770,28848,28938,29026,29122,29212,29794,29883,30130,30411,30663,30948,31341,31818,32040,32262,32538,32765,32995,33225,33455,33685,33912,34331,34557,34982,35212,35640,35859,36142,36350,36481,36708,37134,37359,37786,38007,38432,38552,38828,39129,39453,39744,40058,40195,40326,40431,40673,40840,41044,41252,41523,41635,41747,41852,41969,42183,42329,42469,42555,42903,42991,43237,43655,43904,43986,44084,44676,44776,45028,45452,45707,45801,45890,46127,48151,48393,48495,48748,50904,61436,62952,73583,75111,76868,77494,77914,78975,80240,80496,80732,81279,81773,82378,82576,83156,83720,84095,84213,84751,84908,85104,85377,85633,85803,85944,86008,86373,86740,87416,87680,88018,88371,88465,88651,88957,89219,89344,89471,89710,89921,90040,90233,90410,90865,91046,91168,91427,91540,91727,91829,91936,92065,92340,92848,93344,94221,94515,95085,95234,95966,96138,96222,96558,96650,98716,103962,109351,109413,109991,110575,118522,118635,118864,119024,119176,119347,119513,119682,119849,120012,120255,120425,120598,120769,121043,121242,121447,121777,121861,121957,122053,122151,122251,122353,122455,122557,122659,122761,122861,122957,123069,123198,123321,123452,123583,123681,123795,123889,124029,124163,124259,124371,124471,124587,124683,124795,124895,125035,125171,125335,125465,125623,125773,125914,126058,126193,126305,126455,126583,126711,126847,126979,127109,127239,127351,128249,128395,128539,128677,128743,128833,128909,129013,129103,129205,129313,129421,129521,129601,129693,129791,129901,129979,130085,130177,130281,130391,130513,130676,131466,131546,131646,131736,131846,131936,132177,132271,132377,132469,132569,132681,132795,132911,133027,133121,133235,133347,133449,133569,133691,133773,133877,133997,134123,134221,134315,134403,134515,134631,134753,134865,135040,135156,135242,135334,135446,135570,135637,135763,135831,135959,136103,136231,136300,136395,136510,136623,136722,136831,136942,137053,137154,137259,137359,137489,137580,137703,137797,137909,137995,138099,138195,138283,138401,138505,138609,138735,138823,138931,139031,139121,139231,139315,139417,139501,139555,139619,139725,139811,139921,140005,140409,143025,143143,143258,143338,143699,144285,145689,147033,148394,148782,151557,161646,162686,169499,173800,174551,174813,175660,176039,180317,181171,181400,186008,187018,188970,191370,195494,196238,198369,198709,200020", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,235,236,237,238,239,240,241,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,361,362,363,364,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,442,447,448,449,450,451,459,460,464,468,472,477,483,490,494,498,503,507,511,515,519,523,527,533,537,543,547,553,557,562,566,569,573,579,583,589,593,599,602,606,610,614,618,622,623,624,625,628,631,634,637,641,642,643,644,645,648,650,652,654,659,660,664,670,674,675,677,688,689,693,699,703,704,705,709,736,740,741,745,773,943,969,1140,1166,1197,1205,1211,1225,1247,1252,1257,1267,1276,1285,1289,1296,1304,1311,1312,1321,1324,1327,1331,1335,1339,1342,1343,1348,1353,1363,1368,1375,1381,1382,1385,1389,1394,1396,1398,1401,1404,1406,1410,1413,1420,1423,1426,1430,1432,1436,1438,1440,1442,1446,1454,1462,1474,1480,1489,1492,1503,1506,1507,1512,1513,1518,1611,1681,1682,1692,1701,1702,1855,1859,1862,1865,1868,1871,1874,1877,1880,1884,1887,1890,1893,1897,1900,1904,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1930,1932,1933,1934,1935,1936,1937,1938,1939,1941,1942,1944,1945,1947,1949,1950,1952,1953,1954,1955,1956,1957,1959,1960,1961,1962,1963,1964,1977,1979,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1995,1996,1997,1998,1999,2000,2002,2006,2010,2023,2024,2025,2026,2027,2031,2032,2033,2034,2036,2038,2040,2042,2044,2045,2046,2047,2049,2051,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2067,2068,2069,2070,2072,2074,2075,2077,2078,2080,2082,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2097,2098,2099,2100,2102,2103,2104,2105,2106,2108,2110,2112,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2209,2212,2215,2218,2232,2238,2259,2320,2347,2356,2418,2777,2786,2841,2969,3099,3105,3111,3155,3279,3299,3313,3317,3463,3518,3562,3687,3741,3796,3808,3834,3841", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5627,5686,5745,5804,5863,5917,5971,6024,6078,6132,6186,6240,6515,6594,6667,6741,6812,6884,6956,7029,7086,7144,7217,7291,7365,7440,7512,7585,7655,7726,7786,7847,7916,7985,8055,8129,8205,8269,8346,8422,8499,8564,8633,8710,8785,8854,8922,8999,9065,9126,9223,9288,9357,9456,9527,9586,9644,9701,9760,9824,9895,9967,10039,10111,10183,10250,10318,10386,10445,10508,10572,10662,10753,10813,10879,10946,11012,11082,11146,11199,11266,11327,11394,11507,11565,11628,11693,11758,11833,11906,11978,12027,12088,12149,12210,12272,12336,12400,12464,12529,12592,12652,12713,12779,12838,12898,12960,13031,13091,13159,13876,13963,14216,14303,14391,14473,14556,14646,14737,16423,16481,16526,16592,16656,16713,16770,16824,19004,19052,19101,19152,19390,19773,19822,20077,21234,21612,21674,21734,21791,22129,22199,22277,22331,22401,22486,22534,22580,22641,22704,22770,22834,22905,22968,23033,23097,23158,23219,23271,23344,23418,23487,23562,23636,23710,23851,23921,28457,28843,28933,29021,29117,29207,29789,29878,30125,30406,30658,30943,31336,31813,32035,32257,32533,32760,32990,33220,33450,33680,33907,34326,34552,34977,35207,35635,35854,36137,36345,36476,36703,37129,37354,37781,38002,38427,38547,38823,39124,39448,39739,40053,40190,40321,40426,40668,40835,41039,41247,41518,41630,41742,41847,41964,42178,42324,42464,42550,42898,42986,43232,43650,43899,43981,44079,44671,44771,45023,45447,45702,45796,45885,46122,48146,48388,48490,48743,50899,61431,62947,73578,75106,76863,77489,77909,78970,80235,80491,80727,81274,81768,82373,82571,83151,83715,84090,84208,84746,84903,85099,85372,85628,85798,85939,86003,86368,86735,87411,87675,88013,88366,88460,88646,88952,89214,89339,89466,89705,89916,90035,90228,90405,90860,91041,91163,91422,91535,91722,91824,91931,92060,92335,92843,93339,94216,94510,95080,95229,95961,96133,96217,96553,96645,96923,103957,109346,109408,109986,110570,110661,118630,118859,119019,119171,119342,119508,119677,119844,120007,120250,120420,120593,120764,121038,121237,121442,121772,121856,121952,122048,122146,122246,122348,122450,122552,122654,122756,122856,122952,123064,123193,123316,123447,123578,123676,123790,123884,124024,124158,124254,124366,124466,124582,124678,124790,124890,125030,125166,125330,125460,125618,125768,125909,126053,126188,126300,126450,126578,126706,126842,126974,127104,127234,127346,127486,128390,128534,128672,128738,128828,128904,129008,129098,129200,129308,129416,129516,129596,129688,129786,129896,129974,130080,130172,130276,130386,130508,130671,130828,131541,131641,131731,131841,131931,132172,132266,132372,132464,132564,132676,132790,132906,133022,133116,133230,133342,133444,133564,133686,133768,133872,133992,134118,134216,134310,134398,134510,134626,134748,134860,135035,135151,135237,135329,135441,135565,135632,135758,135826,135954,136098,136226,136295,136390,136505,136618,136717,136826,136937,137048,137149,137254,137354,137484,137575,137698,137792,137904,137990,138094,138190,138278,138396,138500,138604,138730,138818,138926,139026,139116,139226,139310,139412,139496,139550,139614,139720,139806,139916,140000,140120,143020,143138,143253,143333,143694,143927,144797,147028,148389,148777,151552,161456,161776,164038,170066,174546,174808,175008,176034,180312,180918,181395,181546,186218,188096,189277,194391,196233,198364,198704,200015,200218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\24a837476e9b09e595d8982ee1a54f21\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "398", "startColumns": "4", "startOffsets": "24092", "endColumns": "82", "endOffsets": "24170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8b7c6d211140d08bab99021d62acaf57\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,314,2239,2245,3563,3571,3586", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19157,143932,144127,189282,189564,190178", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,314,2244,2249,3570,3585,3601", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19212,144122,144280,189559,190173,190827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0aa6191d710310d82f16cc8f8088c89\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,406,407,408,409,410,411,412,413,415,416,417,418,419,420,421,422,423,3122,3532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,24686,24791,24972,25097,25204,25384,25507,25623,25893,26081,26186,26367,26492,26667,26815,26878,26940,175345,188553", "endLines": "90,91,92,93,94,95,96,97,406,407,408,409,410,411,412,413,415,416,417,418,419,420,421,422,423,3134,3550", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,24786,24967,25092,25199,25379,25502,25618,25721,26076,26181,26362,26487,26662,26810,26873,26935,27014,175655,188965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ed3d9ef39026d9eb9bb9638db04c622\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2011", "startColumns": "4", "startOffsets": "130833", "endLines": "2014", "endColumns": "12", "endOffsets": "131051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2cc8b6019d92a5e15cae9db0f9def0ab\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "365,414", "startColumns": "4,4", "startOffsets": "21796,25726", "endColumns": "67,166", "endOffsets": "21859,25888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1a4d8a5dfb3b4f6de0f4bcf850bbfe59\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2260,2276,2282,3602,3618", "startColumns": "4,4,4,4,4", "startOffsets": "144802,145227,145405,190832,191243", "endLines": "2275,2281,2291,3617,3621", "endColumns": "24,24,24,24,24", "endOffsets": "145222,145400,145684,191238,191365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\32a43f690b0a881d3eb8090abacf32c1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "359", "startColumns": "4", "startOffsets": "21439", "endColumns": "49", "endOffsets": "21484"}}, {"source": "E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1536,1540", "startColumns": "4,4", "startOffsets": "98366,98547", "endLines": "1539,1542", "endColumns": "12,12", "endOffsets": "98542,98711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,120,121,223,224,225,226,227,228,229,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,368,399,400,401,402,403,404,405,443,1965,1966,1970,1971,1975,2130,2131,2787,2804,2974,3007,3037,3070", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6245,6314,13307,13377,13445,13517,13587,13648,13722,14965,15026,15087,15149,15213,15275,15336,15404,15504,15564,15630,15703,15772,15829,15881,16829,16901,16977,17042,17101,17160,17220,17280,17340,17400,17460,17520,17580,17640,17700,17760,17819,17879,17939,17999,18059,18119,18179,18239,18299,18359,18419,18478,18538,18598,18657,18716,18775,18834,18893,19461,19496,20082,20137,20200,20255,20313,20371,20432,20495,20552,20603,20653,20714,20771,20837,20871,20906,21990,24175,24242,24314,24383,24452,24526,24598,28462,127491,127608,127809,127919,128120,140125,140197,161781,162385,170220,171951,172951,173633", "endLines": "29,70,71,88,89,120,121,223,224,225,226,227,228,229,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,368,399,400,401,402,403,404,405,443,1965,1969,1970,1974,1975,2130,2131,2792,2813,3006,3027,3069,3075", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,6309,6372,13372,13440,13512,13582,13643,13717,13790,15021,15082,15144,15208,15270,15331,15399,15499,15559,15625,15698,15767,15824,15876,15938,16896,16972,17037,17096,17155,17215,17275,17335,17395,17455,17515,17575,17635,17695,17755,17814,17874,17934,17994,18054,18114,18174,18234,18294,18354,18414,18473,18533,18593,18652,18711,18770,18829,18888,18947,19491,19526,20132,20195,20250,20308,20366,20427,20490,20547,20598,20648,20709,20766,20832,20866,20901,20936,22055,24237,24309,24378,24447,24521,24593,24681,28528,127603,127804,127914,128115,128244,140192,140259,161979,162681,171946,172627,173628,173795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47a3d3d808bdabe9e0d6e5500da6f30d\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,221,222,425,428,429,430", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13164,13235,27057,27326,27393,27472", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13230,13302,27120,27388,27467,27536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\149a135cc1d249bfe235661b82a9d2f8\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "356", "startColumns": "4", "startOffsets": "21282", "endColumns": "42", "endOffsets": "21320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bf5833745a7339526c1c5d09c1b5350b\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "328,357", "startColumns": "4,4", "startOffsets": "19827,21325", "endColumns": "41,59", "endOffsets": "19864,21380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f8dec0504ea506eec631ea3375271c98\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "396,397", "startColumns": "4,4", "startOffsets": "23926,24008", "endColumns": "81,83", "endOffsets": "24003,24087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\69b8f47af65719d92790e43151456408\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "315,331,360,3028,3033", "startColumns": "4,4,4,4,4", "startOffsets": "19217,19971,21489,172632,172802", "endLines": "315,331,360,3032,3036", "endColumns": "56,64,63,24,24", "endOffsets": "19269,20031,21548,172797,172946"}}]}]}