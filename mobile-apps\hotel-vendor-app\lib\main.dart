import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';

import 'firebase_options.dart';
import 'config/app_theme.dart';
import 'screens/auth/hotel_splash_screen.dart';
import 'providers/hotel_vendor_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    // Firebase initialization failed - app may not work properly
  }

  runApp(const HotelVendorApp());
}

class HotelVendorApp extends StatelessWidget {
  const HotelVendorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => HotelVendorProvider()),
      ],
      child: MaterialApp(
        title: 'Hotel Vendor',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        home: const HotelSplashScreen(),
      ),
    );
  }
}
