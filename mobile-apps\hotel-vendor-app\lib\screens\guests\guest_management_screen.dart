import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../services/firebase_guests_service.dart';
import '../../services/hybrid_auth_service.dart';
import '../../services/aadhar_verification_service.dart';
import 'add_guest_dialog.dart';
import 'aadhar_otp_verification_dialog.dart';

class GuestManagementScreen extends StatefulWidget {
  const GuestManagementScreen({super.key});

  @override
  State<GuestManagementScreen> createState() => _GuestManagementScreenState();
}

class _GuestManagementScreenState extends State<GuestManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _guestsService = FirebaseGuestsService();
  final _authService = HybridAuthService();
  final _aadharService = AadharVerificationService();

  List<Map<String, dynamic>> _currentGuests = [];
  List<Map<String, dynamic>> _checkingInToday = [];
  List<Map<String, dynamic>> _checkingOutToday = [];
  List<Map<String, dynamic>> _allGuests = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadGuestData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadGuestData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize services
      await _authService.initialize();
      final guestServiceInitialized = await _guestsService.initialize();

      if (!guestServiceInitialized) {
        throw Exception(
            'Failed to initialize guest service - user not authenticated');
      }

      await _guestsService.initializeNewUserGuests();

      // Load real guest data from bookings
      await _loadCurrentGuests();
      await _loadTodayCheckIns();
      await _loadTodayCheckOuts();
      await _loadAllGuests();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading guest data: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadCurrentGuests() async {
    _currentGuests = await _guestsService.getCheckedInGuests();

    // Sort current guests by room number
    _currentGuests.sort((a, b) {
      final roomA = a['roomNumber']?.toString() ?? '';
      final roomB = b['roomNumber']?.toString() ?? '';

      // Try to parse as numbers for proper numeric sorting
      final numA = int.tryParse(roomA.replaceAll(RegExp(r'[^0-9]'), ''));
      final numB = int.tryParse(roomB.replaceAll(RegExp(r'[^0-9]'), ''));

      if (numA != null && numB != null) {
        return numA.compareTo(numB);
      }

      // Fallback to string comparison
      return roomA.compareTo(roomB);
    });
  }

  Future<void> _loadTodayCheckIns() async {
    // Get guests checking in today (status = 'Confirmed' or 'Pending' with today's check-in date)
    final allGuests = await _guestsService.getUserGuests();
    final today = DateTime.now();

    _checkingInToday = allGuests.where((guest) {
      final checkInDate = guest['checkInDate'];
      final status = guest['status']?.toString() ?? '';

      if (checkInDate != null) {
        final date =
            checkInDate is DateTime ? checkInDate : checkInDate.toDate();
        final isToday = date.year == today.year &&
            date.month == today.month &&
            date.day == today.day;

        // Include guests with Confirmed or Pending status for today's check-in
        final validStatus = ['Confirmed', 'Pending'].contains(status);

        return isToday && validStatus;
      }
      return false;
    }).toList();

    // Sort by check-in time (earliest first)
    _checkingInToday.sort((a, b) {
      final aDate = a['checkInDate'];
      final bDate = b['checkInDate'];
      if (aDate == null || bDate == null) return 0;

      final dateA = aDate is DateTime ? aDate : aDate.toDate();
      final dateB = bDate is DateTime ? bDate : bDate.toDate();
      return dateA.compareTo(dateB);
    });
  }

  Future<void> _loadTodayCheckOuts() async {
    // Get guests checking out today (status = 'Checked-In' with today's check-out date)
    final allGuests = await _guestsService.getUserGuests();
    final today = DateTime.now();

    _checkingOutToday = allGuests.where((guest) {
      final checkOutDate = guest['checkOutDate'];
      final status = guest['status']?.toString() ?? '';

      if (checkOutDate != null) {
        final date =
            checkOutDate is DateTime ? checkOutDate : checkOutDate.toDate();
        final isToday = date.year == today.year &&
            date.month == today.month &&
            date.day == today.day;

        // Include guests with Checked-In status for today's check-out
        final validStatus = status == 'Checked-In';

        return isToday && validStatus;
      }
      return false;
    }).toList();

    // Sort by check-out time (earliest first)
    _checkingOutToday.sort((a, b) {
      final aDate = a['checkOutDate'];
      final bDate = b['checkOutDate'];
      if (aDate == null || bDate == null) return 0;

      final dateA = aDate is DateTime ? aDate : aDate.toDate();
      final dateB = bDate is DateTime ? bDate : bDate.toDate();
      return dateA.compareTo(dateB);
    });
  }

  Future<void> _loadAllGuests() async {
    _allGuests = await _guestsService.getUserGuests();

    // Sort all guests by status priority and then by creation date
    _allGuests.sort((a, b) {
      final statusA = a['status']?.toString() ?? '';
      final statusB = b['status']?.toString() ?? '';

      // Define status priority (higher priority first)
      final statusPriority = {
        'Checked-In': 5,
        'Confirmed': 4,
        'Pending': 3,
        'Checked-Out': 2,
        'Cancelled': 1,
        'No-Show': 0,
      };

      final priorityA = statusPriority[statusA] ?? 0;
      final priorityB = statusPriority[statusB] ?? 0;

      // First sort by status priority
      if (priorityA != priorityB) {
        return priorityB.compareTo(priorityA); // Higher priority first
      }

      // Then sort by creation date (newest first)
      final createdA = a['createdAt'];
      final createdB = b['createdAt'];

      if (createdA != null && createdB != null) {
        final dateA = createdA is DateTime ? createdA : createdA.toDate();
        final dateB = createdB is DateTime ? createdB : createdB.toDate();
        return dateB.compareTo(dateA); // Newest first
      }

      return 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Guest Management'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: [
            Tab(
              text: 'All Guests',
              icon: Badge(
                label: Text('${_allGuests.length}'),
                child: const Icon(Icons.people),
              ),
            ),
            Tab(
              text: 'Check-in Today',
              icon: Badge(
                label: Text('${_checkingInToday.length}'),
                child: const Icon(Icons.login),
              ),
            ),
            Tab(
              text: 'Check-out Today',
              icon: Badge(
                label: Text('${_checkingOutToday.length}'),
                child: const Icon(Icons.logout),
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddGuestDialog,
            tooltip: 'Add Guest',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadGuestData,
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildGuestList(_allGuests, 'all'),
                _buildGuestList(_checkingInToday, 'checkin'),
                _buildGuestList(_checkingOutToday, 'checkout'),
              ],
            ),
    );
  }

  Widget _buildGuestList(List<Map<String, dynamic>> guests, String type) {
    if (guests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getEmptyIcon(type),
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              _getEmptyMessage(type),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _getEmptySubMessage(type),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _loadGuestData,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                ),
                const SizedBox(width: 16),
                if (type == 'all')
                  ElevatedButton.icon(
                    onPressed: _showAddGuestDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Guest'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: guests.length,
      itemBuilder: (context, index) {
        final guest = guests[index];
        return _buildGuestCard(guest, type);
      },
    );
  }

  Widget _buildGuestCard(Map<String, dynamic> guest, String type) {
    final isAadharVerified = guest['aadharVerified'] ?? false;
    final status = guest['status'] ?? 'confirmed';

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    guest['name']?.substring(0, 1).toUpperCase() ?? 'G',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        guest['name'] ?? 'Unknown Guest',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Room ${guest['roomNumber']} - ${guest['roomType']}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(status),
              ],
            ),
            const SizedBox(height: 16),

            // Aadhar Verification Status
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isAadharVerified ? Colors.green[50] : Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isAadharVerified
                      ? Colors.green[200]!
                      : Colors.orange[200]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isAadharVerified ? Icons.verified_user : Icons.warning,
                    color: isAadharVerified
                        ? Colors.green[600]
                        : Colors.orange[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isAadharVerified
                          ? 'Aadhar Verified: ${guest['aadharNumber']}'
                          : 'Aadhar Verification Pending',
                      style: TextStyle(
                        color: isAadharVerified
                            ? Colors.green[700]
                            : Colors.orange[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (!isAadharVerified)
                    ElevatedButton(
                      onPressed: () => _showAadharVerificationDialog(guest),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                      ),
                      child:
                          const Text('Verify', style: TextStyle(fontSize: 12)),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 12),
            _buildInfoRow('Email', guest['email'] ?? ''),
            _buildInfoRow('Phone', guest['phone'] ?? ''),
            _buildInfoRow('Guests', '${guest['guests'] ?? 1}'),
            _buildInfoRow('Check-in', _formatDate(guest['checkInDate'])),
            _buildInfoRow('Check-out', _formatDate(guest['checkOutDate'])),
            _buildInfoRow('Amount',
                '₹${guest['totalAmount']?.toStringAsFixed(0) ?? '0'}'),
            if (guest['specialRequests']?.isNotEmpty == true)
              _buildInfoRow('Special Requests', guest['specialRequests']),

            const SizedBox(height: 16),
            Row(
              children: [
                if (type == 'checkin')
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _checkInGuest(guest),
                      icon: const Icon(Icons.login, size: 18),
                      label: const Text('Check In'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                if (type == 'checkout')
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _checkOutGuest(guest),
                      icon: const Icon(Icons.logout, size: 18),
                      label: const Text('Check Out'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                if (type == 'current') ...[
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _viewGuestDetails(guest),
                      icon: const Icon(Icons.info, size: 18),
                      label: const Text('Details'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _contactGuest(guest),
                      icon: const Icon(Icons.message, size: 18),
                      label: const Text('Contact'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
                if (type == 'all')
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _viewGuestDetails(guest),
                      icon: const Icon(Icons.info, size: 18),
                      label: const Text('View Details'),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status) {
      case 'confirmed':
        color = Colors.blue;
        label = 'CONFIRMED';
        break;
      case 'checked_in':
        color = Colors.green;
        label = 'CHECKED IN';
        break;
      case 'checked_out':
        color = Colors.grey;
        label = 'CHECKED OUT';
        break;
      default:
        color = Colors.orange;
        label = status.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getEmptyIcon(String type) {
    switch (type) {
      case 'current':
        return Icons.hotel_outlined;
      case 'checkin':
        return Icons.login;
      case 'checkout':
        return Icons.logout;
      default:
        return Icons.people_outline;
    }
  }

  String _getEmptyMessage(String type) {
    switch (type) {
      case 'current':
        return 'No guests currently checked in';
      case 'checkin':
        return 'No check-ins scheduled for today';
      case 'checkout':
        return 'No check-outs scheduled for today';
      default:
        return 'No guest records found';
    }
  }

  String _getEmptySubMessage(String type) {
    switch (type) {
      case 'current':
        return 'Guests will appear here once they check in';
      case 'checkin':
        return 'Check-ins for today will be shown here';
      case 'checkout':
        return 'Check-outs for today will be shown here';
      default:
        return 'Add your first guest to get started with guest management';
    }
  }

  String _formatDate(dynamic date) {
    if (date == null) return '';
    if (date is DateTime) {
      return '${date.day}/${date.month}/${date.year}';
    }
    return date.toString();
  }

  void _showAadharVerificationDialog(Map<String, dynamic> guest) {
    final aadharController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aadhar Verification'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Verify Aadhar for ${guest['name']}'),
            const SizedBox(height: 16),
            TextField(
              controller: aadharController,
              decoration: const InputDecoration(
                labelText: 'Aadhar Number',
                hintText: '1234-5678-9012',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (aadharController.text.isNotEmpty) {
                _verifyAadhar(guest, aadharController.text);
                Navigator.pop(context);
              }
            },
            child: const Text('Verify'),
          ),
        ],
      ),
    );
  }

  Future<void> _verifyAadhar(
      Map<String, dynamic> guest, String aadharNumber) async {
    try {
      // Validate Aadhar format first
      if (!_aadharService.isValidAadharFormat(aadharNumber)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please enter a valid 12-digit Aadhar number'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Show OTP verification dialog
      if (mounted) {
        final result = await showDialog<Map<String, dynamic>>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AadharOtpVerificationDialog(
            aadharNumber: aadharNumber,
            onVerificationComplete: (data) => Navigator.of(context).pop(data),
          ),
        );

        if (result != null && result['isValid'] == true) {
          // Update guest verification status in Firebase
          final success =
              await _guestsService.updateGuestVerification(guest['id'], true);

          if (success) {
            setState(() {
              guest['aadharVerified'] = true;
              guest['aadharNumber'] = aadharNumber;
              guest['name'] = result['name'] ?? guest['name'];
              guest['dateOfBirth'] =
                  result['dateOfBirth'] ?? guest['dateOfBirth'];
              guest['gender'] = result['gender'] ?? guest['gender'];
              guest['address'] = result['address'] ?? guest['address'];
            });

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Aadhar verified successfully for ${guest['customerName'] ?? guest['name']}'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error verifying Aadhar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _checkInGuest(Map<String, dynamic> guest) async {
    try {
      final success = await _guestsService.updateGuestStatus(
        guest['id'],
        'Checked-In',
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${guest['name']} checked in successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          _loadGuestData(); // Refresh data
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to check in guest'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking in guest: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _checkOutGuest(Map<String, dynamic> guest) async {
    try {
      final success = await _guestsService.updateGuestStatus(
        guest['id'],
        'Checked-Out',
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${guest['name']} checked out successfully!'),
              backgroundColor: Colors.blue,
            ),
          );
          _loadGuestData(); // Refresh data
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to check out guest'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking out guest: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _viewGuestDetails(Map<String, dynamic> guest) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Guest Details - ${guest['name']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoRow('Email', guest['email'] ?? ''),
              _buildInfoRow('Phone', guest['phone'] ?? ''),
              _buildInfoRow(
                  'Room', '${guest['roomNumber']} - ${guest['roomType']}'),
              _buildInfoRow('Guests', '${guest['guests'] ?? 1}'),
              _buildInfoRow('Check-in', _formatDate(guest['checkInDate'])),
              _buildInfoRow('Check-out', _formatDate(guest['checkOutDate'])),
              _buildInfoRow('Amount',
                  '₹${guest['totalAmount']?.toStringAsFixed(0) ?? '0'}'),
              _buildInfoRow('Status', guest['status'] ?? ''),
              _buildInfoRow('Aadhar Status',
                  guest['aadharVerified'] ? 'Verified' : 'Pending'),
              if (guest['aadharNumber'] != null)
                _buildInfoRow('Aadhar Number', guest['aadharNumber']),
              if (guest['specialRequests']?.isNotEmpty == true)
                _buildInfoRow('Special Requests', guest['specialRequests']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _contactGuest(Map<String, dynamic> guest) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Contact ${guest['name']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.phone),
              title: Text(guest['phone'] ?? ''),
              subtitle: const Text('Call guest'),
              onTap: () {
                Navigator.pop(context);
                // Implement phone call
              },
            ),
            ListTile(
              leading: const Icon(Icons.email),
              title: Text(guest['email'] ?? ''),
              subtitle: const Text('Send email'),
              onTap: () {
                Navigator.pop(context);
                // Implement email
              },
            ),
            ListTile(
              leading: const Icon(Icons.message),
              title: const Text('Send SMS'),
              subtitle: const Text('Send text message'),
              onTap: () {
                Navigator.pop(context);
                // Implement SMS
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    final searchController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Guests'),
        content: TextField(
          controller: searchController,
          decoration: const InputDecoration(
            labelText: 'Search by name, phone, or room',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Implement search functionality
              Navigator.pop(context);
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showAddGuestDialog() {
    showDialog(
      context: context,
      builder: (context) => AddGuestDialog(
        onGuestAdded: () {
          _loadGuestData(); // Refresh the guest list

          // Show confirmation
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Guest added! Refreshing list...'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        },
      ),
    );
  }
}
